import { useRouter } from "next/router";
import ThingsToDoDetailsPage from "@/src/components/ThingsToDoDetails/ThingsToDoDetailsPage";
import SEO from "@/src/components/SEO.";
import thingsToDo from "@/src/data/thingsToDo";

const DestinationDetails = () => {
  const router = useRouter();
  const { pid } = router.query;

  if (!pid) return <div>Loading...</div>;

  const activityId = parseInt(pid, 10);
  const activity = thingsToDo.find((item) => item.id === activityId);

  if (!activity) {
    return <div>Activity not found</div>;
  }

  return (
    <div>
      <SEO
        title={`${activity.title} | Descubra São Tomé`}
        description={`${activity.description.substring(0, 150)}...`}
        url={`https://descobrasaotome.com/things-to-do-details/${pid}`}
        image={activity.image || '/assets/images/og-default.jpg'}
        type="article"
      />
      <ThingsToDoDetailsPage pid={pid} />
    </div>
  );
};

export default DestinationDetails;