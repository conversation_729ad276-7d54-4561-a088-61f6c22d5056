import Link from 'next/link'
import Image from 'next/image';
const Footer = ({ bg, extraClass }) => {
  return (
    <footer
      className={`main-footer ${bg ? bg : "black"}-bg ${
        extraClass ? extraClass : ""
      }`}
    >
      <div className="container">
        {/*=== Footer CTA ===*/}
      
        {/*=== Footer Widget ===*/}
        <div className="footer-widget-area pt-30 pb-30">
          <div className="row">
            <div className="col-lg-3 col-md-6">
              {/*=== Footer Widget ===*/}
              <div className="footer-widget about-company-widget mb-40">
                <h4 className="widget-title">Sobre</h4>
                <div className="footer-content">
                  <p>
                  Este país encantador possui uma rica tapeçaria de paisagens, 
                  desde as deslumbrantes praias tropicais aos picos cobertos de verde do Pico de São Tomé.
                  </p>
                  <Link href="#" className="footer-logo">
                    <Image 
                      width={200}
                      height={90}
                      src={
                        bg === "gray"
                          ? "/assets/images/logo/sao-tome-logo.png"
                          : "/assets/images/logo/sao-tome-logo.png"
                      }
                      alt="Site Logo"
                    />
                  </Link>
                </div>
              </div>
            </div>
            <div className="col-lg-5 col-md-6">
              {/*=== Footer Widget ===*/}
              <div className="footer-widget service-nav-widget mb-40 pl-lg-70">
                <h4 className="widget-title">Serviços</h4>
                <div className="footer-content">
                  <ul className="footer-widget-nav">
                    <li>
                      <Link href="/restaurants">Restaurantes</Link>
                    </li>
                    <li>
                      <Link href="/hotels">Hotéis</Link>
                    </li>
                    <li>
                      <Link href="/places">Destinos</Link>
                    </li>
                    <li>
                      <Link href="/about">Sobre São Tomé</Link>
                    </li>
                    <li>
                      <Link href="/places">Eventos</Link>
                    </li>
                  </ul>
                  <ul className="footer-widget-nav">
                    <li>
                      <Link href="/">Início</Link>
                    </li>
                    <li>
                      <Link href="/blog">Últimas Notícias e Blog</Link>
                    </li>
                    <li>
                      <Link href="/contact">Contacte-nos</Link>
                    </li>
                    
                  </ul>
                </div>
              </div>
            </div>
            <div className="col-lg-4 col-md-6">
              {/*=== Footer Widget ===*/}
              <div className="footer-widget footer-newsletter-widget mb-40 pl-lg-100">
                <h4 className="widget-title">Newsletter</h4>
                <div className="footer-content">
                  <p>
                    Subscreva a nossa newsletter para receber as últimas 
                    notícias e ofertas especiais
                  </p>
                  <form>
                    <div className="form_group">
                      <label>
                        <i className="far fa-paper-plane" />
                      </label>
                      <input
                        type="email"
                        className="form_control"
                        placeholder="Endereço de Email"
                        name="email"
                        required
                      />
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
        {/*=== Footer Copyright ===*/}
        <div className="footer-copyright">
          <div className="row">
            <div className="col-lg-6">
              {/*=== Footer Text ===*/}
              <div className="footer-text">
                <p>
                  Copyright@ 2024 <span style={{ color: "#F7921E" }}> Descubra São Tomé</span>,
                  Todos os Direitos Reservados
                </p>
              </div>
            </div>
            <div className="col-lg-6">
              {/*=== Footer Nav ===*/}
              <div className="footer-nav float-lg-end">
                <ul>
                  <li>
                    <Link href="/privacy-policy">Política de Privacidade</Link>
                  </li>
                  <li>
                    <Link href="faqs">Perguntas Frequentes</Link>
                  </li>
                  <li>
                    <Link href="/contact">Suporte</Link>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};
export default Footer;
