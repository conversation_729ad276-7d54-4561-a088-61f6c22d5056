import { useState, useEffect } from "react";
import SingleService from "../SingleService/SingleService";
import { Container, Row, Col, Form } from "react-bootstrap";
import styles from "./CuissineFilter.module.css";
import Pagination from "../Pagination/Pagination";

const categoryOptions = [
  "Restaurantes - Chinesa",
  "Restaurantes - Indiana",
  "Restaurantes - Internacional",
  "Restaurantes - Italiana",
  "Restaurantes - Japonesa",
  "Restaurantes - Coreana",
  "Restaurantes - Continental",
  "Restaurantes - Africana",
  "Restaurantes - Mexicana",
  "Restaurantes - Francesa",
  "Restaurantes - Portuguesa",
  "Restaurantes - Caribenha",
];

const TITULO_ID_MAPPING = {
  "Restaurantes - Chinesa": 1056,
  "Restaurantes - Indiana": 1057,
  "Restaurantes - Internacional": 1058,
  "Restaurantes - Italiana": 1059,
  "Restaurantes - Japonesa": 1060,
  "Restaurantes - Coreana": 1061,
  "Restaurantes - Continental": 1073,
  "Restaurantes - Africana": 1402,
  "Restaurantes - Mexicana": 1473,
  "Restaurantes - Francesa": 1474,
  "Restaurantes - Portuguesa": 1475,
  "Restaurantes - Caribenha": 1797,
};

const CuisineFilter = () => {
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const restaurantsPerPage = 10;

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);

      // Build the payload based on the selected category.
      let payload;
      if (selectedCategory === "all") {
        payload = {
          searchParameters: {
            LINGUA_ID: 2,
            PESQUISA_F: "Restaurants",
            LOCALITY_F: "",
            TITULO_ID: 455, 
            country: "STM",
          },
        };
      } else {
        payload = {
          
            searchParameters: {
              LINGUA_ID: 1,
              PESQUISA_F: "Restaurants",
              TITULO_ID: 455,
              country: "STM",
            },
          
        };
      }

      try {
        const response = await fetch(
          "https://api.yellowpageskenya.com/v1/search",
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(payload),
          }
        );

        if (!response.ok) {
          throw new Error("Network response was not ok");
        }

        const result = await response.json();
        console.log("API response:", result);
        // Adjust based on the API response structure:
        const fetchedData = result.data || result.results || [];
        setData(fetchedData);
        // Reset to the first page on new data fetch
        setCurrentPage(1);
      } catch (err) {
        setError(err.message || "Algo deu errado");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [selectedCategory]);

  // Use the 'data' state for pagination
  const indexOfLastRestaurant = currentPage * restaurantsPerPage;
  const indexOfFirstRestaurant = indexOfLastRestaurant - restaurantsPerPage;
  const currentRestaurants = data.slice(
    indexOfFirstRestaurant,
    indexOfLastRestaurant
  );

  const onPageChange = (newPage) => {
    setCurrentPage(newPage);
  };

  return (
    <Container className={`py-4 ${styles.container}`}>
      <Row className="justify-content-center mb-4">
        <Col md={8}>
          <h1 className={`text-center mb-3 ${styles.heading}`}>
            Filtrar por Cozinha
          </h1>
          <Form>
            <Form.Group controlId="cuisine-select">
              <Form.Label className={styles.filterLabel}>
                Selecionar Cozinha:
              </Form.Label>
              <Form.Control
                as="select"
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
              >
                <option value="all">Todos os Restaurantes</option>
                {categoryOptions.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </Form.Control>
            </Form.Group>
          </Form>
        </Col>
      </Row>

      {loading && (
        <Row className="justify-content-center">
          <Col md={12} className="text-center">
            <p>Carregando restaurantes...</p>
          </Col>
        </Row>
      )}
      {error && (
        <Row className="justify-content-center">
          <Col md={12} className="text-center text-danger">
            <p>Erro: {error}</p>
          </Col>
        </Row>
      )}

      <Row>
        {currentRestaurants && currentRestaurants.length > 0 ? (
          currentRestaurants.map((item) => (
            <Col key={item.nome_loc_tit_id} md={4} className="mb-4">
              <SingleService
                service={item}
                url="https://www.yellowpageskenya.com/business-category/services/business-details"
                baseUrl="https://www.yellowpageskenya.com"
              />
            </Col>
          ))
        ) : (
          !loading && (
            <Col md={12} className="text-center">
              <p>Nenhum restaurante encontrado para a categoria selecionada.</p>
            </Col>
          )
        )}
      </Row>
      <Pagination
        currentPage={currentPage}
        totalPages={Math.ceil(data.length / restaurantsPerPage)}
        onPageChange={onPageChange}
      />
    </Container>
  );
};

export default CuisineFilter;
