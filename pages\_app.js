// Import necessary modules
import PreLoader from "@/src/layout/PreLoader";
import "@/styles/globals.css";
import Head from "next/head";
import Script from 'next/script';
import { Fragment, useEffect, useState } from "react";



export default function App({ Component, pageProps }) {
  const [loader, setLoader] = useState(true);

  const handleLoader = () => {
    setTimeout(() => {
      setLoader(false);
    }, 1500);
  };

  useEffect(() => {
    handleLoader();
  }, []);

  // ... existing code

  return (
    <Fragment>
      <Head>
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
        {/* <link rel="stylesheet" href="css/animate.css" /> */}
        
      </Head>
       
        {/* <Script src="js/wow.min.js"></Script> */}
      {loader && <PreLoader />}
      {!loader && <Component {...pageProps} />}
    </Fragment>
  );
}
