import Home3Slider from "@/src/components/sliders/Home3";
import Layout from "@/src/layout/Layout";
import {
  sliderActive4Item,
  sliderActive3ItemDot, 
} from "@/src/sliderProps";
import dynamic from "next/dynamic";
import Link from "next/link";
import Slider from "react-slick";
import places from "@/src/data/places";
import Image from "next/image";
import SEO from "@/src/components/SEO.";

const Counter = dynamic(() => import("@/src/components/Counter"), {
  ssr: false,
});
const Index = () => {
  return (
    <div>
    <SEO 
        title="Seu guia turístico definitivo | Descubra São Tomé" 
        description="Descubra as melhores experiências de viagem de São Tomé e Príncipe, praias paradisíacas e eventos culturais" 
        url="https://descobrasaotome.com" 
        image="/assets/images/destinations/diani.png" 
      />
    <Layout header={2} >
      {/*====== Start Hero Section ======*/}
      <Home3Slider />
      <section className="about-section pt-50 " id="about" >
        <div className="container-fluid">
          <div className="row justify-content-center">
            <div className="col-xl-6 col-lg-9">
              {/*=== About Content Box ===*/}
              <div className="about-content-box text-center mb-55 wow fadeInDown">
                <div className="section-title mb-30">
                  <span className="sub-title">Sobre Descubra São Tomé</span>
                  <h2>Explore as Verdadeiras Aventuras e Viagens de São Tomé e Príncipe</h2>
                </div>
                <p>
                Situado ao longo do Equador, este país encantador possui uma
                rica tapeçaria de paisagens, desde as savanas deslumbrantes
                até aos picos cobertos de verde do Pico de São Tomé.
                </p>
              </div>
            </div>
          </div>
          <Slider
            {...sliderActive4Item}
            className="slider-active-4-item wow fadeInUp"
          >
            {/*=== Features Image Item ===*/}
            <div className="single-features-item mb-40">
              <div className="img-holder">
                <Image fill={true}
                  src="/assets/images/features/hotel.png"
                  alt="Features Image"
                />
                <div className="content">
                  <div className="text">
                    <h4 className="title">Hotéis</h4>
                    <Link href="/hotels" className="icon-btn">
                      <i className="far fa-arrow-right" />
                    </Link>
                  </div>
                  <p>São Tomé oferece uma gama diversificada de hotéis, desde luxo... </p>
                </div>
              </div>
            </div>
            {/*=== Features Image Item ===*/}
            <div className="single-features-item mb-40">
              <div className="img-holder">
                <Image fill={true}
                  src="/assets/images/features/restaurant.png"
                  alt="Features Image"
                />
                <div className="content">
                  <div className="text">
                    <h4 className="title">Restaurantes</h4>
                    <Link href="/restaurants" className="icon-btn">
                      <i className="far fa-arrow-right" />
                    </Link>
                  </div>
                  <p>Os restaurantes são-tomenses mostram a rica diversidade culinária...</p>
                </div>
              </div>
            </div>
            {/*=== Features Image Item ===*/}
            <div className="single-features-item mb-40">
              <div className="img-holder">
                <Image fill={true}
                  src="/assets/images/features/destinations.png"
                  alt="Features Image"
                />
                <div className="content">
                  <div className="text">
                    <h4 className="title">Destinos</h4>
                    <Link href="/destinations" className="icon-btn">
                      <i className="far fa-arrow-right" />
                    </Link>
                  </div>
                  <p>Os destinos de São Tomé vão desde a icónica Praia Banana para... </p>
                </div>
              </div>
            </div>
            {/*=== Features Image Item ===*/}
            <div className="single-features-item mb-40">
              <div className="img-holder">
                <Image fill={true}
                  src="/assets/images/features/activities.png"
                  alt="Features Image"
                />
                <div className="content">
                  <div className="text">
                    <h4 className="title">Atividades </h4>
                    <Link href="/things-to-do" className="icon-btn">
                      <i className="far fa-arrow-right" />
                    </Link>
                  </div>
                  <p>São Tomé oferece uma variedade de atividades para visitantes, incluindo clássicas...</p>
                </div>
              </div>
            </div>
          </Slider>
        </div>
      </section>    
      {/*====== End Hero Section ======*/}

         {/*====== Start Places Section ======*/}
        <section className="places-seciton pt-95 pb-100 mb-100"> 
        <div className="container">
          <div className="row">
            <div className="col-lg-12">
              {/*====== Section Title ======*/}
              <div className="section-title text-center mb-45 wow fadeInDown">
                <h2>Tours em Destaque</h2>
              </div>
            </div>
          </div>
          <Slider
            {...sliderActive3ItemDot}
            className="slider-active-3-item-dot wow fadeInUp "
          >
          {places.map((place) => (
            
            <div key={place.id} className="single-place-item mb-60">
              <div className="place-img">
              <Image width={410} height={280} src={place.image} alt={place.name} />
              </div>
              <div className="place-content">
                <div className="info">
                <ul className="ratings">
                {Array(5)
                  .fill()
                  .map((_, index) => (
                    <li key={index}>
                      <i className="fas fa-star" />
                    </li>
                  ))}
                <li>
                  <Link href="#">({place.rating})</Link>
                </li>
              </ul>
                  <h4 className="title">
                   {place.name}
                  </h4>
                  <p className="location">
                    <i className="fas fa-map-marker-alt" />
                    {place.location}
                  </p>
                  
                  <div className="meta">
                    <span>
                      <i className="far fa-clock" />
                    {place.duration}
                    </span>
                    <span>
                      <i className="far fa-user" />
                    {place.capacity}
                    </span>
                    <span>
                    {/*  <Link legacyBehavior href="/tour-details">
                        <div>
                          Details
                          <i className="far fa-long-arrow-right" />
                        </div>
                      </Link> */} 
                    </span>
                  </div>
                </div>
              </div>
            </div> 
          ))}
    
          </Slider>
        </div>
      </section>
      {/*====== End Places Section ======*/}

      {/*====== Start Fun Section ======*/}
      <section className="fun-section">
        <div className="container">
          {/*=== Fun Wrapper ===*/}
          <div
            className="fun-wrapper pt-60 pb-20 bg_cover"
            style={{ backgroundImage: "url(assets/images/bg/map.png)" }}
          >
            <div className="row">
              <div className="col-lg-3 col-sm-6">
                {/*=== Counter Item ===*/}
                <div className="single-counter-item-two mb-30">
                  <div className="inner-counter text-center">
                    <div className="icon">
                      <i className="far fa-suitcase" />
                    </div>
                    <div className="content text-white">
                      <h2 className="number">
                        <Counter end={356} />+
                      </h2>
                      <p>Viajantes Felizes</p>
                    </div>
                  </div>
                </div>

              </div>
              <div className="col-lg-3 col-sm-6">
                {/*=== Counter Item ===*/}
                <div className="single-counter-item-two mb-30">
                  <div className="inner-counter text-center">
                    <div className="icon">
                      <i className="far fa-plane" />
                    </div>
                    <div className="content text-white">
                      <h2 className="number">
                        <Counter end={95} />+
                      </h2>
                      <p>Destinos</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="col-lg-3 col-sm-6">
                {/*=== Counter Item ===*/}
                <div className="single-counter-item-two mb-30">
                  <div className="inner-counter text-center">
                    <div className="icon">
                      <i className="far fa-star" />
                    </div>
                    <div className="content text-white">
                      <h2 className="number">
                        <Counter end={99} />%
                      </h2>
                      <p>Avaliações Positivas</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="col-lg-3 col-sm-6">
                {/*=== Counter Item ===*/}
                <div className="single-counter-item-two mb-30">
                  <div className="inner-counter text-center">
                    <div className="icon">
                      <i className="far fa-hotel" />
                    </div>
                    <div className="content text-white">
                      <h2 className="number">
                        <Counter end={240} />
                        +
                      </h2>
                      <p>Hotéis e Restaurantes</p>
                    </div>
                  </div>
                </div>
              </div>

            </div>
          </div>
        </div>
      </section>

      {/*====== End Fun Section ======*/}

      {/*====== Start CTA Section ======*/}
      <section  
        className="cta-bg overlay bg_cover pt-150 pb-150 mt-60"
        style={{ backgroundImage: "url(assets/images/bg/cta-bg.jpg)" }}
      >
        <div className="container">
          <div className="row align-items-center">
            <div className="col-xl-7 col-lg-8">
              {/*=== CTA Content Box ===*/}
              <div className="cta-content-box text-white">
                <h2 className="mb-35">
                  Experimente Aventuras Reais e Paisagens Incomparáveis 
                </h2>
                <Link legacyBehavior href="/about">
                  <div className="main-btn secondary-btn">  
                    Verificar Disponibilidade
                    <i className="far fa-paper-plane" />
                  </div>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
      {/*====== End CTA Section ======*/}

      {/*====== Start Blog Section ======*/}
      <section className="blog-section pt-100 pb-60">
        <div className="container">
          <div className="row justify-content-center">
            <div className="col-lg-7">
              <div className="section-title text-center mb-45">
                <span className="sub-title">Notícias e Blog</span>
                <h2>Nossos Últimos Blogs</h2>
              </div>
            </div>
          </div>
          <div className="row justify-content-center">
            <div className="col-lg-4 col-md-6 col-sm-12">
              {/*=== Single Blog Post ===*/}
              <div className="single-blog-post-three mb-40">
                <div className="post-thumbnail">
                  <Image  width={640} height={247} src="/assets/images/blog/lamu.jpeg" alt="Blog Image" />
                </div>
                <div className="entry-content">
                  <div className="post-meta">
                    <span>
                      <i className="far fa-calendar-alt" />
                      <Link href="#">15 de Novembro de 2023 </Link>
                    </span>
                    <h3 className="title">
                      <Link legacyBehavior href="/blog-details">
                        <div>Descobrindo São Tomé: Um Guia de Aventuras Inesquecíveis</div>
                      </Link>
                    </h3>
                    <Link legacyBehavior href="/blog-details">
                      <div className="main-btn filled-btn">
                        Ler Mais
                        <i className="far fa-paper-plane" />
                      </div>
                    </Link>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-lg-4 col-md-6 col-sm-12">
              {/*=== Single Blog Post ===*/}
              <div className="single-blog-post-three mb-40">
                <div className="post-thumbnail">
                  <Image  width={640} height={247} src="/assets/images/blog/namib.jpg" alt="Blog Image" />
                </div>
                <div className="entry-content">
                  <div className="post-meta">
                    <span>
                      <i className="far fa-calendar-alt" />
                      <Link href="#">15 de Novembro de 2022</Link>
                    </span>
                    <h3 className="title">
                      <Link legacyBehavior href="/blog2">
                        <div>
                        Parques Naturais de São Tomé: Obras-primas da Natureza Reveladas
                        </div>
                      </Link>
                    </h3>
                    <Link legacyBehavior href="/blog2">
                      <div className="main-btn filled-btn">
                        Ler Mais
                        <i className="far fa-paper-plane" />
                      </div>
                    </Link>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-lg-4 col-md-6 col-sm-12">
              {/*=== Single Blog Post ===*/}
              <div className="single-blog-post-three mb-40">
                <div className="post-thumbnail">
                  <Image width={640} height={247} src="/assets/images/blog/lake.jpeg" alt="Blog Image" />
                </div>
                <div className="entry-content">
                  <div className="post-meta">
                    <span>
                      <i className="far fa-calendar-alt" />
                      <Link href="#">14 de Junho de 2023</Link>
                    </span>
                    <h3 className="title">
                      <Link legacyBehavior href="/blog5">
                        <div>
                        O que Torna o Arquipélago Especial
                        </div>
                      </Link>
                    </h3>
                    <Link legacyBehavior href="/blog5">
                      <div className="main-btn filled-btn">
                        Ler Mais
                        <i className="far fa-paper-plane" />
                      </div>
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      {/*====== End Blog Section ======*/}

    </Layout>
    </div>
  );
};
export default Index;
