import Link from "next/link";
import Menu from "../Menu";
import Image from "next/image";

const Header2 = () => {
  return (
    <header className="header-area header-one transparent-header">
      {/*====== Header Navigation ======*/}
      <div className="header-navigation navigation-white">
        <div className="nav-overlay" />
        <div className="container-fluid">
          <div className="primary-menu">
            {/*====== Site Branding ======*/}
            <div className="site-branding">
              <Link legacyBehavior href="index">
                <div className="brand-logo">
                  <Image 
                    width={200}
                    height={90}
                    src="/assets/images/logo/sao-tome-logo.png"
                    alt="Site Logo"
                  />
                </div>
              </Link>
            </div>
            {/*====== Nav Menu ======*/}
            <div className="nav-menu">
              {/*====== Site Branding ======*/}
              <div className="mobile-logo mb-30 d-block d-xl-none">
                <Link legacyBehavior href="/">
                  <div className="brand-logo">
                    <Image 
                      width={200}
                      height={90}
                      src="/assets/images/logo/logo-black.png"
                      alt="Site Logo"
                    />
                  </div>
                </Link>
              </div>
              {/*=== Nav Search ===*/}
              <div className="nav-search mb-30 d-block d-xl-none ">
                <form>
                  <div className="form_group">
                    <input
                      type="email"
                      className="form_control"
                      placeholder="Pesquisar Aqui"
                      name="email"
                      required
                    />
                    <button className="search-btn">
                      <i className="fas fa-search" />
                    </button>
                  </div>
                </form>
              </div>
              {/*====== main Menu ======*/}
              <Menu />
              {/*====== Menu Button ======*/}
              <div className="menu-button mt-40 d-xl-none">
                <Link legacyBehavior href="/contact">
                  <div className="main-btn secondary-btn">
                    Contacte-nos
                    <i className="far fa-paper-plane">
                     
                    </i>
                  </div>
                </Link>
              </div>
            </div>
            {/*====== Nav Right Item ======*/}
            <div className="nav-right-item">
              <div className="menu-button d-xl-block d-none">
                <Link legacyBehavior href="/contact">
                  <div className="main-btn primary-btn">
                    Contacte-nos
                    <i className="far fa-paper-plane">
                    
                    </i>
                  </div>
                </Link>
              </div>
              <div className="navbar-toggler">
                <span />
                <span />
                <span />
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};
export default Header2;
