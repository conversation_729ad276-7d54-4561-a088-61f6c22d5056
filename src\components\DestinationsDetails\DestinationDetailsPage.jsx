import { Col, Container, Row, Table, Accordion } from "react-bootstrap";
import destinationDetails from "@/src/data/destinationDetails";
import styles from "./DestinationsDetails.module.css"
import Layout from "@/src/layout/Layout";
import HotelBanner from "../HotelBanner";
import Image from "next/image";
import Head from "next/head";

export default function DestinationsDetailsPage({ pid }) {
  const destination = destinationDetails.find((item) => item.id === parseInt(pid));
  if (!destination) {
    return <p>Destination not found</p>;
  }

  // Tourist Attraction Schema
  const touristAttractionSchema = {
    "@context": "https://schema.org",
    "@type": "TouristAttraction",
    "name": destination.discoverTitle,
    "description": destination.texts[0] || destination.discoverTitle,
    "image": destination.image,
    "url": `https://descobrasaotome.com/destinations-details/${pid}`,
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "ST",
      "addressLocality": "São Tomé e <PERSON>rínci<PERSON>"
    }
  };

  return (
    <Layout header={2} extraClass={"pt-10"}>
      <Head>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(touristAttractionSchema)
          }}
        />
      </Head>
    <HotelBanner pageTitle={destination.discoverTitle}  />
    <section className={styles.destinationsDetails}>
      <Container>
        <Row>
          <Col xl={8} lg={7}>
            <div className={styles.destinationImage}>
              <Image fill={true}
                src={destination.image}
                alt={destination.discoverTitle}
                className="img-fluid"
              />
            </div>

            <h1 className={styles.destinationTitle}>{destination.discoverTitle}</h1>

            <div className={styles.destinationTexts}>
              {destination.texts.map((text, index) => (
                <p key={index}>{text}</p>
              ))}
            </div>

            <h3 className="mt-4">Overview</h3>
            <Table bordered hover className={styles.overviewTable}>
              <tbody>
                {destination.overviews.map((overview) => (
                  <tr key={overview.id}>
                    <td><strong>{overview.left}</strong></td>
                    <td>{overview.right}</td>
                  </tr>
                ))}
              </tbody>
            </Table>

            <h3 className="mt-4">FAQs</h3>
            <Accordion defaultActiveKey="0">
            {destination.faqs.map((faq, index) => (
              <Accordion.Item eventKey={String(index)} key={faq.id}>
                <Accordion.Header>{faq.question}</Accordion.Header>
                <Accordion.Body>{faq.answer}</Accordion.Body>
              </Accordion.Item>
            ))}
          </Accordion>
          
          </Col>
          <Col xl={4} lg={5}>
            {/* Optional right-side content can go here */}
          </Col>
        </Row>
      </Container>
    </section>
    </Layout>
    
  );
}


