import DestinationsPage from "@/src/components/HotelsPage/HotelsPage";
import HotelBanner from "@/src/components/HotelBanner";
import Layout from "@/src/layout/Layout";
import Link from "next/link";
import SEO from "@/src/components/SEO.";
const Hotels = () => {
  return (
    <div>
    <SEO 
    title="Hotéis | Descubra São Tomé" 
    description="Descubra as melhores experiências hoteleiras de São Tomé e Príncipe, desde hotéis de luxo a pousadas à beira-mar" 
    url="https://descobrasaotome.com" 
    image="/assets/images/place/desert.png" 
  />
    <Layout header={2} extraClass={"pt-10"} >
      <HotelBanner pageTitle={"Hotéis"} />
      {/*====== Start Destination Section ======*/}
     <DestinationsPage />
     
      {/*====== End Destination Section ======*/}
      {/*====== Start CTA Section ======*/}
      <section
        className="cta-bg overlay bg_cover pt-150 pb-150"
        style={{ backgroundImage: "url(assets/images/bg/cta-bg.jpg)" }}
      >
        <div className="container">
          <div className="row align-items-center">
            <div className="col-xl-7 col-lg-8">
              {/*=== CTA Content Box ===*/}
              <div className="cta-content-box text-white wow fadeInLeft">
                <h2 className="mb-35">
                  Pronto para Viajar com Aventura Real e Desfrutar da Natureza
                </h2>
                <Link legacyBehavior href="/about">
                  <div className="main-btn secondary-btn">
                    Verificar Disponibilidade
                    <i className="far fa-paper-plane" />
                  </div>
                </Link>
              </div>
            </div>
            <div className="col-xl-5 col-lg-4">
              {/*=== Play Box ===*/}
              <div className="play-box text-lg-end text-center wow fadeInRight">
                
              </div>
            </div>
          </div>
        </div>
      </section>
      {/*====== End CTA Section ======*/}

      {/*====== Start Gallery Section ======*/}
      {/*     <GallerySection />          */}
      {/*====== End Gallery Section ======*/}
    </Layout>
    </div>
  );
};
export default Hotels;
