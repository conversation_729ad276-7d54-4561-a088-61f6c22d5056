@import url("/assets/vendor/bootstrap/css/bootstrap.min.css");
@import url("/assets/fonts/flaticon/flaticon_gowilds.css");
@import url("/assets/fonts/fontawesome/css/all.min.css");
@import url("/assets/vendor/magnific-popup/dist/magnific-popup.css");
@import url("/assets/vendor/slick/slick.css");
@import url("/assets/vendor/jquery-ui/jquery-ui.min.css");
@import url("/assets/vendor/nice-select/css/nice-select.css");
@import url("/assets/css/default.css");
@import url("/assets/css/style.css");
@import url("/assets/vendor/animate.css");

/* image popup */
img.mfp-img {
  box-shadow: 0 0 8px rgb(0 0 0 / 60%);
  position: absolute;
  max-height: 392px;
  padding: 0 !important;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.mfp-img-container .mfp-content {
  max-width: 400px !important;
}
.mfp-img-container .mfp-close {
  top: -110px;
  right: -24px;
}

/* Custom Yellow Button */
.btn-custom-yellow {
  background-color: #F7921E;
  color: white;
  border: 2px solid #F7921E;
  padding: 8px 16px;
  border-radius: 5px;
  text-decoration: none;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.3s ease;
  display: inline-block;
}

.btn-custom-yellow:hover {
  background-color: transparent;
  color: #F7921E;
  border-color: #F7921E;
  text-decoration: none;
}


img {
  max-width: 100%;
  display: block;
  object-position: center;
}