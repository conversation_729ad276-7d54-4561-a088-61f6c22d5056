import PageBanner from "@/src/components/PageBanner";
import Layout from "@/src/layout/Layout";
import Link from "next/link";
import Image from "next/image";
import SEO from "@/src/components/SEO.";
const Contact = () => {
  return (
    <div>
      <SEO
        title="Contacte-nos | Descubra São Tomé"
        description="Contacte-nos se tiver alguma pergunta sobre viajar em São Tomé e Príncipe."
        url="https://descobrasaotome.com"
        image="/assets/images/contact/contact.jpg"
      />
      <Layout header={2}>
        <PageBanner pageTitle={"Contacte-nos"} />
        {/*====== Start Info Section ======*/}
        <section className="contact-info-section pt-100 pb-60">
          <div className="container">
            <div className="row justify-content-center">
              <div className="col-xl-8">
                {/*=== Section Title ===*/}
                <div className="section-title text-center mb-45 wow fadeInDown">
                  <span className="sub-title">Contacte-nos</span>
                  <h3>Fale Connosco Hoje</h3>
                </div>
              </div>
            </div>
            <div className="row justify-content-center">
              <div className="col-lg-4 col-md-6 col-sm-12">
                {/*=== Contact Info Item ===*/}
                <div className="contact-info-item text-center mb-40 wow fadeInUp">
                  <div className="icon">
                    <Image
                      width={100}
                      height={100}
                      src="/assets/images/icon/icon-1.png"
                      alt="icon"
                    />
                  </div>
                  <div className="info">
                    <span className="title">Localização do Escritório</span>
                    <p>Avenida 12 de Julho, São Tomé</p>
                  </div>
                </div>
              </div>
              <div className="col-lg-4 col-md-6 col-sm-12">
                {/*=== Contact Info Item ===*/}
                <div className="contact-info-item text-center mb-40 wow fadeInDown">
                  <div className="icon">
                    <Image
                      width={100}
                      height={100}
                      src="/assets/images/icon/icon-2.png"
                      alt="icon"
                    />
                  </div>
                  <div className="info">
                    <span className="title">Endereço de Email</span>
                    <p>
                      <Link href="mailto:<EMAIL>">
                        <EMAIL>
                      </Link>
                    </p>
                  </div>
                </div>
              </div>
              <div className="col-lg-4 col-md-6 col-sm-12">
                {/*=== Contact Info Item ===*/}
                <div className="contact-info-item text-center mb-40 wow fadeInUp">
                  <div className="icon">
                    <Image
                      width={100}
                      height={100}
                      src="/assets/images/icon/icon-3.png"
                      alt="icon"
                    />
                  </div>
                  <div className="info">
                    <span className="title">Linha Direta</span>
                    <p>
                      <Link href="tel:(+*************">
                        (+*************
                      </Link>
                    </p>
                    <p>
                      <Link href="tel:(+*************">
                        (+*************
                      </Link>
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
        {/*====== End Info Section ======*/}
        {/*====== Start Contact Map Section ======*/}
        <section className="contact-page-map pb-100 wow fadeInUp">
          {/*=== Map Box ===*/}
          <div className="map-box">
            <iframe
              src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d127783.81689234716!2d6.473611!3d0.3301924!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x1069cd01a3907be1%3A0xb5d1e4a5e0f8ac5c!2sSao%20Tome!5e0!3m2!1sen!2sst!4v1639995845070!5m2!1sen!2sst"
              width="600"
              height="450"
            ></iframe>
          </div>
        </section>
        {/*====== End Contact Map Section ======*/}
        {/*====== Start Contact Section ======*/}
        <section className="contact-section pb-100">
          <div className="container">
            <div className="row justify-content-center">
              <div className="col-xl-6">
                <div className="section-title text-center mb-50 wow fadeInDown">
                  <span className="sub-title">Entre em Contacto</span>
                  <h2>Envie-nos uma Mensagem</h2>
                </div>
              </div>
            </div>
            <div className="row justify-content-center">
              <div className="col-lg-10">
                <div className="contact-area wow fadeInUp">
                  <form
                    className="contact-form"
                    onSubmit={(e) => {
                      // Let HTML5 validation handle errors automatically
                      if (!e.target.checkValidity()) {
                        e.preventDefault();
                        e.target.reportValidity();
                      }
                    }}
                  >
                    <div className="row">
                      <div className="col-lg-6">
                        <div className="form_group">
                          <input
                            type="text"
                            placeholder="Nome"
                            className="form_control"
                            name="name"
                            pattern="[A-Za-z\s]+"
                            title="Por favor use apenas letras e espaços."
                            onKeyPress={(event) => {
                              const char = String.fromCharCode(event.which);
                              if (!/^[A-Za-z\s]+$/.test(char)) {
                                event.preventDefault();
                              }
                            }}
                            required
                          />
                        </div>
                      </div>
                      <div className="col-lg-6">
                        <div className="form_group">
                          <input
                            type="text"
                            placeholder="Número de Telefone"
                            className="form_control"
                            name="number"
                            pattern="\d+"
                            title="Por favor insira apenas números."
                            onKeyPress={(event) => {
                              const char = String.fromCharCode(event.which);
                              if (!/^\d+$/.test(char)) {
                                event.preventDefault();
                              }
                            }}
                            required
                          />
                        </div>
                      </div>
                      <div className="col-lg-6">
                        <div className="form_group">
                          <input
                            type="email"
                            placeholder="Endereço de Email"
                            className="form_control"
                            name="email"
                            required
                          />
                        </div>
                      </div>
                      <div className="col-lg-6">
                        <div className="form_group">
                          <input
                            type="text"
                            placeholder="Assunto"
                            className="form_control"
                            name="subject"
                            required
                          />
                        </div>
                      </div>
                      <div className="col-lg-12">
                        <div className="form_group">
                          <textarea
                            name="message"
                            placeholder="Escreva a Mensagem"
                            className="form_control"
                            rows={6}
                            required
                          />
                        </div>
                      </div>
                      <div className="col-lg-12">
                        <div className="form_group text-center">
                          <button className="main-btn primary-btn">
                            Enviar Mensagem
                            <i className="far fa-paper-plane" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </section>
        {/*====== End Contact Section ======*/}
      </Layout>
    </div>
  );
};
export default Contact;
