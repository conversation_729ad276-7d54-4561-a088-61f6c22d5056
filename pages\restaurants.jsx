import RestaurantsPage from "@/src/components/RestaurantsPage/RestaurantsPage";
import RestaurantBanner from "@/src/components/RestaurantBanner";
import Layout from "@/src/layout/Layout";
import Link from "next/link";
import Description from "@/src/components/Description/Description";
import SEO from "@/src/components/SEO.";
const Restaurants = () => {

  const title = "Restaurantes em São Tomé e Príncipe";
  const description =
    "São Tomé e Príncipe possui uma cena culinária vibrante com uma mistura de sabores locais e internacionais. Desde estabelecimentos de jantar requintados na capital até restaurantes à beira-mar servindo frutos do mar frescos, há algo para todos os paladares. Desfrute de pratos tradicionais são-tomenses como Calulu, peixe grelhado e muamba de galinha, ou explore cozinhas internacionais em ambientes elegantes. Seja procurando um café casual ou uma experiência gastronómica luxuosa, os restaurantes de São Tomé oferecem sabores inesquecíveis e hospitalidade calorosa.";
  const features = [
    "Culinária Autêntica São-tomense",
    "Experiências de Jantar Requintado",
    "Opções de Frutos do Mar Frescos",
    "Menus Veganos e Vegetarianos",
    "Restaurantes Familiares",
    "Jantar com Vista para o Mar",
    "Petiscos e Comida de Rua",
    "Cozinhas Internacionais (Italiana, Portuguesa, Francesa, etc.)",
    "Jantares Românticos à Luz de Velas",
    "Serviços de Takeaway e Entrega",
  ];
  return (
    <div>
    <SEO 
        title="Restaurantes | Descubra São Tomé" 
        description="Descubra os melhores restaurantes de São Tomé e Príncipe, desde restaurantes com vista para o mar até diferentes cozinhas" 
        url="https://descobrasaotome.com" 
        image="/assets/images/thingstodo/the hub.webp" 
      />
    <Layout header={2} extraClass={"pt-160"}>
      <RestaurantBanner pageTitle={"Restaurantes"} />
      {/*====== Start Destination Details Section ======*/}
      <Description title={title} description={description} features={features} />;
      {/*====== End Destination Details Section ======*/}

      <RestaurantsPage />
      {/*====== Start CTA Section ======*/}
      <section
        className="cta-bg overlay bg_cover pt-150 pb-150 "
        style={{ backgroundImage: "url(assets/images/bg/cta-bg2.jpg)" }}
      >
        <div className="container ">
          <div className="row align-items-center">
            <div className="col-xl-7 col-lg-8">
              {/*=== CTA Content Box ===*/}
              <div className="cta-content-box text-white wow fadeInLeft">
                <h2 className="mb-35">
                  Pronto para Viajar com Aventura Real e Desfrutar da Natureza
                </h2>
                <Link legacyBehavior href="/about">
                  <div className="main-btn primary-btn">
                    Verificar Disponibilidade
                    <i className="far fa-paper-plane" />
                  </div>
                </Link>
              </div>
            </div>
            <div className="col-xl-5 col-lg-4">
              {/*=== Play Box ===*/}
              <div className="play-box text-lg-end text-center wow fadeInRight">
                <div
                  href="https://www.youtube.com/watch?v=ibuUmMhD2Pg"
                  className="video-popup"
                >
                  <i className="fas fa-play" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      {/*====== End CTA Section ======*/}
     
      {/*====== Start Gallery Section ======*/}
    {/*      <GallerySection className="mt-100" />     */}  
    </Layout>
    </div>
  );
};
export default Restaurants;
