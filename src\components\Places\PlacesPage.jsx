import React from "react";
import Slider from "react-slick";
import { sliderActive3ItemDot } from "@/src/sliderProps";
import Image from "next/image";
import Link from "next/link";
import Description from "../Description/Description";

const events = [
  {
    image: "/assets/images/place/festival-de-jazz.webp",
    title: "Festival Internacional de Jazz de São Tomé",
    location: "Centro Cultural Franco-São-tomense",
    price: "15000",
    date: "Sáb, 24 Fev, 2024",
    link: "/tour-details",
  },
  {
    image: "/assets/images/place/festival-da-cultura.webp",
    title: "Festival da Cultura São-tomense 2024",
    location: "Estádio Nacional 12 de Julho",
    price: "25000",
    date: "Dom, 07 Abr, 2024",
    link: "/tour-details",
  },
  {
    image: "/assets/images/place/mostra-de-arte.webp",
    title: "Mostra de Arte Contemporânea",
    location: "Museu Nacional de São Tomé",
    price: "8000",
    date: "Sáb, 24 Fev, 2024",
    link: "/tour-details",
  },
  {
    image: "/assets/images/place/noite-de-fados.webp",
    title: "Noite de Fados e Puita",
    location: "Casa das Artes de São Tomé",
    price: "12000",
    date: "Dom, 04 Fev, 2024",
    link: "/tour-details",
    attendees: "25",
  },
];

const title = "Lugares e Eventos em São Tomé e Príncipe";
const description =
  "São Tomé e Príncipe é um centro de destinos vibrantes e eventos emocionantes que celebram sua rica cultura, história e beleza natural. Das ruas movimentadas da capital às paisagens serenas das plantações de cacau, há sempre um lugar para explorar. Participe de festivais tradicionais, presencie eventos culturais emocionantes, ou mergulhe nas tradições locais através da arte, música e gastronomia. A energia dinâmica de São Tomé garante experiências inesquecíveis para todos.";
const features = [
  "Ilhéu das Rolas – Linha do Equador",
  "Pico de São Tomé – Montanha Mais Alta",
  "Praia Banana – Paraíso Tropical",
  "Festival da Cultura São-tomense",
  "Festa de São Lourenço – Celebração Cultural",
  "Festival de Jazz de São Tomé – Música e Arte",
  "Lagoa Azul – Natureza Exuberante",
  "Roça São João dos Angolares",
  "Festival Internacional de Cinema",
  "Festivais de Arte e Música na Capital",
];

const PlacesPage = () => (
  <div>
    {/*====== Start Places Section ======*/} 
    <section className="places-seciton pt-45 pb-100">
      <div className="container">
      <Description title={title} description={description} features={features} />;

        <Slider {...sliderActive3ItemDot} className="slider-active-3-item-dot wow fadeInUp">
          {events.map((event, index) => (
            <div key={index} className="single-place-item mb-60">
              <div className="place-img">
                <Image width={600} height={400} src={event.image} alt="Imagem do Local" style={{ objectFit: 'contain' }} />
              </div>
              <div className="place-content">
                <div className="info">
                  <h4 className="title">
                    <Link legacyBehavior href={event.link}>
                      <div>{event.title}</div>
                    </Link>
                  </h4>
                  <p className="location">
                    <i className="fas fa-map-marker-alt" />
                    {event.location}
                  </p>
                  <p className="price">
                    <i className="fas fa-usd-circle" />
                    A partir de <span className="currency">Db </span>{event.price}
                  </p>
                  <div className="meta">
                    <span>
                      <i className="far fa-calendar" />
                      {event.date}
                    </span>
                    {event.attendees && (
                      <span>
                        <i className="far fa-user" />
                        {event.attendees}
                      </span>
                    )}
                    <span>
                      <Link legacyBehavior href={event.link}>
                        <div>
                          Detalhes
                          <i className="far fa-long-arrow-right" />
                        </div>
                      </Link>
                    </span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </Slider>
      </div>
    </section>
    {/*====== End Places Section ======*/} 
  </div>
);

export default PlacesPage;
