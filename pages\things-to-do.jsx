import ThingsToDoPage from "@/src/components/ThingsToDoPage/thingstodo";
import ThingsToDoBanner from "@/src/components/ThingsToDoBanner";
import Layout from "@/src/layout/Layout";
import Link from "next/link";
import SEO from "@/src/components/SEO.";

const thingstoDo = () => {
  return (
    <div>
    <SEO 
        title="Things to Do | Travel Discover Kenya" 
        description="Discover things to do in Kenya, from events, to things to do" 
        url="https://traveldiscoverkenya.com" 
        image="/assets/images/thingstodo/beer-district.webp" 
      />
    <Layout header={2} extraClass={"pt-10"} >
      <ThingsToDoBanner pageTitle={"Things To Do"} />
      {/*====== Start Destination Section ======*/}
     <ThingsToDoPage/>
     
      {/*====== End Destination Section ======*/}
      {/*====== Start CTA Section ======*/} 
      <section
        className="cta-bg overlay bg_cover pt-150 pb-150"
        style={{ backgroundImage: "url(assets/images/bg/cta-bg.jpg)" }}
      >
        <div className="container">
          <div className="row align-items-center">
            <div className="col-xl-7 col-lg-8">
              {/*=== CTA Content Box ===*/}
              <div className="cta-content-box text-white wow fadeInLeft">
                <h2 className="mb-35">
                  Ready to Travel With Real Adventure and Enjoy Natural
                </h2>
                <Link legacyBehavior href="/about">
                  <div className="main-btn secondary-btn">
                    Check Availability
                    <i className="far fa-paper-plane" />
                  </div>
                </Link>
              </div>
            </div>
            <div className="col-xl-5 col-lg-4">
              {/*=== Play Box ===*/}
              <div className="play-box text-lg-end text-center wow fadeInRight">
                
              </div>
            </div>
          </div>
        </div>
      </section>
    </Layout>
    </div>
  );
};
export default thingstoDo;
