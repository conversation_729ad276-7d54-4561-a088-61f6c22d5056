import { useRouter } from 'next/router'
import DestinationsDetailsPage from '@/src/components/DestinationsDetails/DestinationDetailsPage'
import destinationDetails from '@/src/data/destinationDetails'

const DestinationsDetails = () => {
    const router = useRouter()
    const { pid } = router.query

    const destination = destinationDetails.find((item) => item.id === parseInt(pid));
    if (!destination) {
      return <p>Destination not found</p>;
    }


  return ( 
      <DestinationsDetailsPage pid={pid} />
  )
}

export default DestinationsDetails