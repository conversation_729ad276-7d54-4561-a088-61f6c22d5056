import { useRouter } from 'next/router'
import DestinationsDetailsPage from '@/src/components/DestinationsDetails/DestinationDetailsPage'
import destinationDetails from '@/src/data/destinationDetails'
import SEO from '@/src/components/SEO.'

const DestinationsDetails = () => {
    const router = useRouter()
    const { pid } = router.query

    const destination = destinationDetails.find((item) => item.id === parseInt(pid));
    if (!destination) {
      return <p>Destination not found</p>;
    }


  return (
      <div>
        <SEO
          title={`${destination.discoverTitle} | Descubra São Tomé`}
          description={`Descubra ${destination.discoverTitle} em São Tomé e Príncipe. ${destination.texts[0] || 'Explore este destino incrível.'}`}
          url={`https://descobrasaotome.com/destinations-details/${pid}`}
          image={destination.image || '/assets/images/og-default.jpg'}
          type="article"
        />
        <DestinationsDetailsPage pid={pid} />
      </div>
  )
}

export default DestinationsDetails