// pages/_document.js
import Document, { Html, <PERSON>, Main, NextScript } from "next/document";

class MyDocument extends Document {
  render() {
    return (
      <Html lang="pt">
        <Head>
          {/* Meta tags */}
          <meta charSet="utf-8" />
          <meta httpEquiv="x-ua-compatible" content="ie=edge" />
          <meta name="description" content="Descubra São Tomé e Príncipe - Aventuras, Tours, Viagens" />
          <meta name="author" content="Descubra São Tomé" />
          <meta name="keywords" content="São Tomé, Príncipe, turismo, viagens, hotéis, restaurantes, destinos, atividades" />

          {/* Favicon */}
          <link rel="shortcut icon" href="/assets/images/favicon.ico" type="image/png" />

          {/* Google Fonts */}
          <link href="https://fonts.googleapis.com/css2?family=Prompt:wght@300;400;500;600;700;800&display=swap" rel="stylesheet" />
        </Head>
        <body>
          <Main />
          <NextScript />
        </body>
      </Html>
    );
  }
}

export default MyDocument;
