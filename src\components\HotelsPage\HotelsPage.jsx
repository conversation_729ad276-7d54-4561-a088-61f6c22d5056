import React, { useState, useEffect } from "react";
import axios from "axios";
import Pagination from "../Pagination/pagination";
import { Container, Row, Col } from "react-bootstrap";
import Description from "../Description/Description";
import PreLoader from "@/src/layout/PreLoader";
import SingleService from "../SingleService/SingleService";

const DestinationsPage = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const hotelsPerPage = 10;
  const [hotels, setHotels] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Calculate indices for current hotels
  const indexOfLastHotel = currentPage * hotelsPerPage;
  const indexOfFirstHotel = indexOfLastHotel - hotelsPerPage;
  const currentHotels = hotels.slice(indexOfFirstHotel, indexOfLastHotel);

  const onPageChange = (newPage) => {
    setCurrentPage(newPage);
  };

  useEffect(() => {
    const fetchHotels = async () => {
      try {
        setLoading(true);
        const response = await axios.post(
          "https://api.yellowpageskenya.com/v1/search",
          {
            searchParameters: {
              LINGUA_ID: 2,
              PESQUISA_F: "hotels",
              LOCALITY_F: "",
              TITULO_ID: "",
              country: "STM",
            },
          },
          {
            headers: {
              "Content-Type": "application/json",
            },
          }
        );

        setHotels(response.data.results || []);
      } catch (error) {
        setError(error.message || "Ocorreu um erro ao carregar os dados");
      } finally {
        setLoading(false);
      }
    };

    fetchHotels();
  }, []);

  const title = "Hotéis em São Tomé e Príncipe";
  const description =
    "São Tomé e Príncipe oferece uma gama diversificada de hotéis, desde lodges de luxo com vista para praias pristinas até acomodações acolhedoras na capital. Os visitantes podem escolher entre eco-lodges, resorts à beira-mar no Oceano Atlântico, ou hotéis na cidade, garantindo uma estadia confortável adaptada às suas preferências e às paisagens únicas do arquipélago.";
  const features = [
    "Ecoturismo",
    "Observação de Vida Selvagem",
    "Mergulho e Snorkeling",
    "Caminhadas na Natureza",
    "Wi-fi Gratuito",
    "Lodges de Luxo com Vista para o Mar",
    "Resorts à Beira-mar com Vistas do Oceano",
    "Hotéis Boutique com Serviço Personalizado",
    "Pousadas Económicas",
    "Suítes Familiares e Comodidades para Crianças",
    "Instalações para Conferências e Eventos",
    "Pacotes Tudo Incluído",
    "Piscinas Infinitas com Vista para Paisagens Cénicas",
    "Centros de Bem-estar e Spas",
  ];

  return (
    <div>
        <section className="content-container pt-100">
      <Container>
        <Description
          title={title}
          description={description}
          features={features}
        />
        {loading ? (
         <PreLoader/>
        ) : error ? (
          <p>Erro: {error}</p>
        ) : (
          <>
            <Row className="restaurant-parent-container">
              {currentHotels.map((destination) => (
                   <SingleService
                     service={destination}
                     key={destination.id}
                     url={"https://www.yellowpageskenya.com/business-category/hotels/business-details"}
                     baseUrl={"https://www.yellowpageskenya.com"}
                   />
               ))}
            </Row>

            <div className="wow fadeInDown mt-60 mb-30 d-flex justify-content-center">
              <Pagination
                currentPage={currentPage}
                totalPages={Math.ceil(hotels.length / hotelsPerPage)}
                onPageChange={onPageChange}
              />
            </div>
          </>
        )}
      </Container>
    </section>
    </div>
  );
};

export default DestinationsPage;
