import React from "react";
import { Container, Row, Col, Table, Accordion } from "react-bootstrap";
import Layout from "@/src/layout/Layout";
import styles from "./ThingsToDoDetailsPage.module.css";
import thingsToDo from "@/src/data/thingsToDo";
import ThingsToDoBanner from "../ThingsToDoBanner";
import Image from "next/image";

const ThingsToDoDetailsPage = ({ pid }) => {
  const activityId = parseInt(pid, 10);
  const activity = thingsToDo.find((item) => item.id === activityId);

  if (!activity) {
    return (
      <Layout header={2} extraClass={"pt-10"}>
        <Container className="text-center my-5">
          <h1>Activity Not Found</h1>
          <p>We couldn't find the activity you're looking for.</p>
        </Container>
      </Layout>
    );
  }

  return (
    <Layout header={2} extraClass={"pt-10"}>
      {/* Banner Section */}
      <ThingsToDoBanner pageTitle={activity.title} />

      <section className={styles.detailsSection}>
        <Container>
          <Row>
            {/* Main Content */}
            <Col xl={8} lg={7}>
              <div className={styles.activityImage}>
                <Image fill={true}
                  src={activity.image}
                  alt={activity.title}
                  className="img-fluid"
                />
              </div>

              <h1 className={styles.activityTitle}>{activity.title}</h1>

              <div className={styles.activityTexts}>
                <p>{activity.description}</p>
              </div>

              {/* Overview Section */}
              <h3 className={styles.sectionTitle}>Overview</h3>
              <Table bordered hover className={styles.overviewTable}>
                <tbody>
                  <tr>
                    <td><strong>Location</strong></td>
                    <td>{activity.location}</td>
                  </tr>
                  <tr>
                    <td><strong>Operating Hours</strong></td>
                    <td>{activity.operatingTime}</td>
                  </tr>
                  <tr>
                    <td><strong>Category</strong></td>
                    <td>{activity.category}</td>
                  </tr>
                </tbody>
              </Table>

              {/* FAQs Section */}
              <h3 className={styles.sectionTitle}>FAQs</h3>
              <Accordion defaultActiveKey="0">
                <Accordion.Item eventKey="0">
                  <Accordion.Header>Is there an entry fee?</Accordion.Header>
                  <Accordion.Body>
                    Entry fees depend on the specific activity. Check at the entrance or official website for details.
                  </Accordion.Body>
                </Accordion.Item>
                <Accordion.Item eventKey="1">
                  <Accordion.Header>What should I bring?</Accordion.Header>
                  <Accordion.Body>
                    Comfortable clothing, water, and sunscreen are recommended for most activities.
                  </Accordion.Body>
                </Accordion.Item>
              </Accordion>
            </Col>

            {/* Optional Sidebar or Additional Content */}
            <Col xl={4} lg={5}>
              <div className={styles.sidebar}>
                <h4>Additional Information</h4>
                <p>Explore nearby attractions, restaurants, or lodging options to make the most of your visit.</p>
              </div>
            </Col>
          </Row>
        </Container>
      </section>
    </Layout>
  );
};

export default ThingsToDoDetailsPage;
