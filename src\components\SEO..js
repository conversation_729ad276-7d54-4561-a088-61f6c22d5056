import Head from 'next/head';
import { NextSeo } from 'next-seo';

const SEO = ({ title, description, url, image, type = 'website', article }) => {
  const defaultImage = '/assets/images/og-default.jpg';
  const siteUrl = 'https://descobrasaotome.com';

  // JSON-LD structured data
  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "TravelAgency",
    "name": "Descubra São Tomé",
    "url": siteUrl,
    "logo": `${siteUrl}/assets/images/logo.png`,
    "description": "Seu guia turístico definitivo para descobrir São Tomé e Príncipe",
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "ST",
      "addressLocality": "São Tomé"
    },
    "sameAs": [
      "https://facebook.com/descobrasaotome",
      "https://instagram.com/descobrasaotome"
    ]
  };

  const articleSchema = article ? {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": title,
    "description": description,
    "image": image || defaultImage,
    "author": {
      "@type": "Organization",
      "name": "Descubra São Tomé"
    },
    "publisher": {
      "@type": "Organization",
      "name": "Descubra São Tomé",
      "logo": {
        "@type": "ImageObject",
        "url": `${siteUrl}/assets/images/logo.png`
      }
    },
    "datePublished": article.datePublished,
    "dateModified": article.dateModified || article.datePublished
  } : null;

  return (
    <div>
      <Head>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="robots" content="index, follow" />
        <link rel="canonical" href={url} />

        {/* JSON-LD Structured Data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(organizationSchema)
          }}
        />
        {articleSchema && (
          <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{
              __html: JSON.stringify(articleSchema)
            }}
          />
        )}
      </Head>
      <NextSeo
        title={title}
        description={description}
        canonical={url}
        openGraph={{
          url: url,
          title: title,
          description: description,
          type: type,
          images: [
            {
              url: image || defaultImage,
              width: 1200,
              height: 630,
              alt: title,
            },
          ],
          site_name: 'Descubra São Tomé',
        }}
        twitter={{
          cardType: 'summary_large_image',
          site: '@descobrasaotome',
          title: title,
          description: description,
          image: image || defaultImage,
        }}
      />
    </div>
  );
};

export default SEO;
