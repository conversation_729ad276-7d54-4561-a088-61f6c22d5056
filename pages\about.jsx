import React from 'react';
import AboutBanner from "@/src/components/AboutBanner";
import Layout from "@/src/layout/Layout";
import Link from "next/link";
import Slider from "react-slick";
import {
  sliderActive3ItemDot, 
} from "@/src/sliderProps";
import places from "@/src/data/places";
import Image from 'next/image';
import SEO from '@/src/components/SEO.';
const About = () => {
  return (
    <div>

    <SEO 
    title="Sobre São Tomé | Descubra São Tomé" 
    description="Explore as paisagens deslumbrantes de São Tomé e Príncipe, cultura rica e praias paradisíacas. Descubra os melhores destinos turísticos e experiências." 
    url="https://descobrasaotome.com" 
    image="/assets/images/about/giraffe.png" 
  />

    <Layout header={2} >
      
      <AboutBanner pageTitle={"Sobre São Tomé"} />
      {/*====== Start Features Section ======*/}
      <section className="features-section pt-60 pb-0">
        <div className="container">
          <div className="row align-items-xl-center">
            <div className="col-xl-8">
              {/*=== Features Content Box ===*/}
              <div className="features-content-box pr-lg-70 mb-50 wow fadeInLeft">
                {/*=== Section Title ===*/}
                <div className="section-title mb-30">
                  <span className="sub-title">Disponibilidade</span>
                  <h3>Explore Descubra São Tomé</h3>
                </div>
                <p className="mb-30">
                O nosso lar é um mosaico de culturas, cada uma contribuindo para um património tão diverso quanto as paisagens que nos definem. Em cada sorriso trocado, sentirá o calor de um povo orgulhoso das suas raízes e ansioso por partilhar a magia da sua terra.
                São Tomé e Príncipe é mais do que um destino; é uma experiência esperando para se desenrolar. Quer anseie pela emoção de explorar a natureza virgem, a tranquilidade de praias pristinas, ou a adrenalina de escalar montanhas, São Tomé oferece uma tapeçaria de atividades para satisfazer a alma de qualquer aventureiro.
                <br/> Embarque numa jornada através do tempo enquanto explora a arquitetura colonial portuguesa, ou deixe as cores vibrantes da fauna e flora tropical deixarem uma marca indelével na sua memória. <br/>Para aqueles que procuram o fascínio da vida citadina, São Tomé pulsa com energia, misturando modernidade com um rico pano de fundo histórico.
                Mas não são apenas os lugares que cativam corações; são as experiências únicas gravadas em cada aventura são-tomense. Testemunhe a desova das tartarugas, onde milhares de tartarugas marinhas pintam as praias num espetáculo que desafia a imaginação. Envolva-se com as comunidades locais, saboreando a autenticidade das danças tradicionais e sabores tentadores que fazem da culinária são-tomense uma jornada em si mesma.

                </p>
                <Link href="#" className="main-btn filled-btn">
                  Saber Mais
                  <i className="far fa-paper-plane" />
                </Link>
              </div>
            </div>
            <div className="col-xl-4">
              <div> 
                <Image
                  width={640}
                  height={960}
                    src="/assets/images/about/about-sao-tome.webp"
                    className="rounded mb-40"
                    alt="Features Image"
                  />
              </div>
            </div>
          </div>
        </div>
      </section>
      {/*====== End Features Section ======*/}
      
      {/*====== Start Places Section ======*/}
      <section className="places-seciton pt-35 pb-50 mb50"> 
        <div className="container">
          <div className="row">
            <div className="col-lg-12">
              {/*====== Section Title ======*/}
              <div className="section-title text-center mb-45 wow fadeInDown">
                <h2>Tours em Destaque</h2>
              </div>
            </div>
          </div>
          <Slider
          {...sliderActive3ItemDot}
          className="slider-active-3-item-dot wow fadeInUp "
        >
        {places.map((place) => (
          
          <div key={place.id} className="single-place-item mb-60">
            <div className="place-img">
            <Image width={410} height={280} src={place.image} alt={place.name} />
            </div>
            <div className="place-content">
              <div className="info">
              <ul className="ratings">
              {Array(5)
                .fill()
                .map((_, index) => (
                  <li key={index}>
                    <i className="fas fa-star" />
                  </li>
                ))}
              <li>
                <Link href="#">({place.rating})</Link>
              </li>
            </ul>
                <h4 className="title">
                 {place.name}
                </h4>
                <p className="location">
                  <i className="fas fa-map-marker-alt" />
                  {place.location}
                </p>
                
                <div className="meta">
                  <span>
                    <i className="far fa-clock" />
                  {place.duration}
                  </span>
                  <span>
                    <i className="far fa-user" />
                  {place.capacity}
                  </span>
                  <span>
                  {/*  <Link legacyBehavior href="/tour-details">
                      <Link>
                        Details
                        <i className="far fa-long-arrow-right" />
                      </Link>
                    </Link> */} 
                  </span>
                </div>
              </div>
            </div>
          </div> 
        ))}
  
        </Slider>
        </div>
      </section>
      {/*====== End Places Section ======*/}

      {/*====== Start CTA Section ======*/}
      <section
        className="cta-bg overlay bg_cover pt-150 pb-150"
        style={{ backgroundImage: "url(assets/images/bg/cta-bg.jpg)" }}
      >
        <div className="container">
          <div className="row justify-content-center">
            <div className="col-xl-8">
              {/*=== CTA Content Box ===*/}
              <div className="cta-content-box text-center text-white wow fadeInDown">
                <h2 className="mb-35">
                  Pronto para Viajar com Aventura Real e Desfrutar da Natureza
                </h2>
                <Link legacyBehavior href="/contact">
                  <div className="main-btn primary-btn">
                    Verificar Disponibilidade
                    <i className="far fa-paper-plane" />
                  </div>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
      {/*====== End CTA Section ======*/}


    </Layout>
    </div>
  );
};
export default About;
