import BlogBanner from "@/src/components/BlogBanner";
import Layout from "@/src/layout/Layout";
import Link from "next/link";
import Image from "next/image";
import SEO from "@/src/components/SEO.";

const blogPosts = [
  {
    image: "/assets/images/blog/mountains.png",
    title: "Descobrindo São Tomé: Guia do Viajante para Aventuras Inesquecíveis",
    slug: "descobrindo-sao-tome-guia-do-viajante-para-aventuras-inesqueciveis",
    author: "Descubra São Tomé",
    date: "15 de Novembro, 2023",
    readMoreLink: "/blog-details"
  },
  {
    image: "/assets/images/blog/gorongosa.png",
    title: "<PERSON><PERSON><PERSON> Protegi<PERSON> de São Tomé: Obras-Primas da Natureza Reveladas",
    slug: "areas-protegidas-de-sao-tome",
    author: "Descubra São Tomé",
    date: "15 de Outubro, 2023",
    readMoreLink: "/blog2"
  },
  {
    image: "/assets/images/blog/vacation.jpg",
    title: "O Ecoturismo de São Tomé: Uma Jornada na Natureza Selvagem",
    slug: "ecoturismo-de-sao-tome",
    author: "Descubra São Tomé",
    date: "05 de Agosto, 2023",
    readMoreLink: "/blog3"
  },
  {
    image: "/assets/images/place/ololo.png",
    title: "",
    slug: "",
    author: "Descubra São Tomé",
    date: "",
    readMoreLink: ""
  }
];
const BlogList = () => {
  return (
    <div>
    <SEO 
    title="Lista de Blogs | Descubra São Tomé" 
    description="Descubra nossos blogs mais recentes sobre São Tomé e Príncipe, das últimas tendências e eventos aos novos lugares para visitar." 
    url="https://descobrasaotome.com" 
    image="/assets/images/place/brandy.png" 
  />
    <Layout header={2}>
      <BlogBanner pageTitle={"Notícias & Blogs"} />
      {/*====== Start Blog Section ======*/}
      <section className="blog-list-section pt-100 pb-70">
        <div className="container">
          <div className="row">
            <div className="col-xl-8">
              {/*=== Blog List Wrapper ===*/}
              <div className="blog-list-wrapper">
                {blogPosts.map((post, idx) => (
                <div key={idx} className="single-blog-post-four mb-30 wow fadeInDown">
                  <div className="post-thumbnail">
                    <Image width={410} height={290}
                      src={post.image}
                      alt="Imagem do Post"
                      
                    />
                  </div>
                  <div className="entry-content">
                    <div className="post-meta">
                      <span>
                        <i className="far fa-calendar-alt" />
                        <Link href="#">15 de Novembro, 2023</Link>
                      </span>
                    </div>
                    <h3 className="title pb-20">
                      <Link legacyBehavior prefetch={true} href="/blog-details">
                        <div>
                        {post.title}
                        </div>
                      </Link>
                    </h3>
                    <h6 className="author">
                      <i className="far fa-user" />
                      <Link href="#">{post.author}</Link>
                    </h6>
                    <Link legacyBehavior href={`/blog-details/${post.slug}`}>
                      <div className="main-btn filled-btn">
                        Ler Mais
                        <i className="fas fa-paper-plane" />
                      </div>
                    </Link>
                  </div>
                </div>
                ))}
              </div>
              {/*=== Pagination ===*/}
              {/* <ul className="tdk-pagination wow fadeInDown mt-60 mb-30">
                <li className="me-2">
                  <Link href="#">
                    <i className="far fa-arrow-left" />
                  </Link>
                </li>
                <li className="me-2">
                  <Link href="#" className="active">
                    01
                  </Link>
                </li>
                <li className="me-2">
                  <Link href="#">02</Link>
                </li>
                <li className="me-2">
                  <Link href="#">03</Link>
                </li>
                <li className="me-2">
                  <Link href="#">04</Link>
                </li>
                <li className="me-2">
                  <Link href="#">
                    <i className="far fa-arrow-right" />
                  </Link>
                </li>
              </ul> */}
            </div>
            <div className="col-xl-4">
              <div className="sidebar-widget-area">
               
           
                {/*=== Category Widget ===*/}
                <div className="sidebar-widget category-widget mb-30 wow fadeInUp">
                  <h4 className="widget-title">Categoria</h4>
                  <ul className="category-nav">
                    <li>
                      <Link href="/restaurants">
                        Restaurantes
                        <i className="far fa-arrow-right" />
                      </Link>
                    </li>
                    <li>
                      <Link href="/hotels">
                        Hotéis
                        <i className="far fa-arrow-right" />
                      </Link>
                    </li>
                    <li>
                      <Link href="/places">
                        Lugares
                        <i className="far fa-arrow-right" />
                      </Link>
                    </li>
                    <li>
                      <Link href="#">
                        Atividades <i className="far fa-arrow-right" />
                      </Link>
                    </li>
                    <li>
                      <Link href="#">
                        Eventos <i className="far fa-arrow-right" />
                      </Link>
                    </li>
                  </ul>
                </div>
                {/*=== Recent Post Widget ===*/}
                <div className="sidebar-widget recent-post-widget mb-40 wow fadeInUp">
                  <h4 className="widget-title">Notícias Recentes</h4>
                  <ul className="recent-post-list">
                    <li className="post-thumbnail-content">
                      <Image
                        width={410} height={290}
                        src="/assets/images/blog/post-thumb-1.jpg"
                        alt="miniatura do post"
                      />
                      <div className="post-title-date">
                        <h5>
                          <Link legacyBehavior href="#">
                            Como fazer as malas para sua próxima aventura
                          </Link>
                        </h5>
                        <span className="posted-on">
                          <i className="far fa-calendar-alt" />
                          <Link href="#">23 de Novembro, 2022</Link>
                        </span>
                      </div>
                    </li>
                    <li className="post-thumbnail-content">
                      <Image
                        width={410} height={290}
                        src="/assets/images/blog/post-thumb-2.jpg"
                        alt="miniatura do post"
                      />
                      <div className="post-title-date">
                        <h5>
                          <Link legacyBehavior href="#">
                            Como relaxar durante suas próximas férias
                          </Link>
                        </h5>
                        <span className="posted-on">
                          <i className="far fa-calendar-alt" />
                          <Link href="#">23 de Novembro, 2022</Link>
                        </span>
                      </div>
                    </li>
                    <li className="post-thumbnail-content">
                      <Image
                        width={410} height={290}
                        src="/assets/images/blog/post-thumb-3.jpg"
                        alt="miniatura do post"
                      />
                      <div className="post-title-date">
                        <h5>
                          <Link legacyBehavior href="#">
                            Dicas importantes de viagem para São Tomé e Príncipe
                          </Link>
                        </h5>
                        <span className="posted-on">
                          <i className="far fa-calendar-alt" />
                          <Link href="#">23 de Novembro, 2022</Link>
                        </span>
                      </div>
                    </li>
                  </ul>
                </div>
                {/*=== Banner Widget ===*/}
                <div className="sidebar-widget sidebar-banner-widget wow fadeInUp mb-40">
                  <div className="banner-widget-content">
                    <div className="banner-img">
                      <Image
                        width={410} height={290}
                        src="/assets/images/place/brandy.png"
                        alt="Banner do Post"
                      />
                      <div className="hover-overlay">
                        <div className="hover-content">
                          <h4 className="title">
                            <Link href="#">Roça São João dos Angolares</Link>
                          </h4>
                          <p>
                            <i className="fas fa-map-marker-alt" />
                            Caué, São Tomé e Príncipe
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                {/*=== Tag Widget ===*/}
                <div className="sidebar-widget tag-cloud-widget gray-bg mb-40 wow fadeInUp">
                  <h4 className="widget-title">Atividades Turísticas</h4>
                  <Link href="#">Ecoturismo</Link>
                  <Link href="#">Observação de Aves</Link>
                  <Link href="#">Mergulho</Link>
                  <Link href="#">Snorkeling</Link>
                  <Link href="#">Trekking</Link>
                  <Link href="#">Observação de Tartarugas</Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      {/*====== End Blog Section ======*/}

    </Layout>
    </div>
  );
};
export default BlogList;
