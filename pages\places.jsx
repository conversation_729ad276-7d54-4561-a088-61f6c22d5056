import HotelBanner from "@/src/components/HotelBanner";
import Layout from "@/src/layout/Layout";
import PlacesPage from "@/src/components/Places/PlacesPage";
import SEO from "@/src/components/SEO.";

const Places = () => {
  return (
    <div>
    <SEO 
        title="Lugares | Descubra São Tomé" 
        description="Descubra os melhores lugares para visitar em São Tomé e Príncipe, desde destinos paradisíacos a eventos culturais" 
        url="https://descobrasaotome.com" 
        image="/assets/images/place/brandy.png" 
      />
    <Layout header={2} extraClass={"pt-160"} >
      <HotelBanner pageTitle={"Lugares para Visitar"}  />
      {/*====== Start Booking Section ======*/}
      
      {/*====== End Booking Section ======*/}
      {/*====== Start Destination Section ======*/}
     <PlacesPage/>
     
      {/*====== End Destination Section ======*/}
      

      {/*====== Start Gallery Section ======*/}
      {/*     <GallerySection />          */}
      {/*====== End Gallery Section ======*/}
    </Layout>
    </div>
  );
};
export default Places;
